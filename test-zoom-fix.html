<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=0.9, maximum-scale=0.9, minimum-scale=0.9, user-scalable=no">
    <title>缩放修复测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            position: fixed;
            top: 0;
            left: 0;
        }
        
        body {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            position: fixed;
            top: 0;
            left: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        #app {
            width: 100vw;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            overflow: hidden;
            zoom: 0.9;
        }
        
        .container {
            width: 100%;
            height: 100%;
            display: flex;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            background-size: 400% 400%;
            animation: gradientShift 10s ease infinite;
        }
        
        .sidebar {
            width: 240px;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .main-content {
            flex: 1;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-family: Arial, sans-serif;
            text-align: center;
        }
        
        .test-box {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .corner-indicator {
            position: absolute;
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.8);
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }
        
        .top-left {
            top: 0;
            left: 0;
            border-radius: 0 0 25px 0;
        }
        
        .top-right {
            top: 0;
            right: 0;
            border-radius: 0 0 0 25px;
        }
        
        .bottom-left {
            bottom: 0;
            left: 0;
            border-radius: 0 25px 0 0;
        }
        
        .bottom-right {
            bottom: 0;
            right: 0;
            border-radius: 25px 0 0 0;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .info {
            font-size: 18px;
            margin: 10px 0;
        }
        
        .small-text {
            font-size: 14px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <!-- 角落指示器 -->
            <div class="corner-indicator top-left">左上</div>
            <div class="corner-indicator top-right">右上</div>
            <div class="corner-indicator bottom-left">左下</div>
            <div class="corner-indicator bottom-right">右下</div>
            
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="test-box">
                    <h3>侧边栏</h3>
                    <p class="small-text">240px 宽度</p>
                    <p class="small-text">100% 高度</p>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="main-content">
                <div class="test-box">
                    <h1>缩放修复测试</h1>
                    <div class="info">当前缩放: 90%</div>
                    <div class="info">视口填充: 100%</div>
                    <div class="small-text">如果四个角落的指示器都贴边显示，</div>
                    <div class="small-text">且没有空白区域，则修复成功！</div>
                </div>
                
                <div class="test-box">
                    <h3>检查项目</h3>
                    <div class="small-text">✓ 页面缩放到90%</div>
                    <div class="small-text">✓ 完全填充浏览器窗口</div>
                    <div class="small-text">✓ 没有空白边距</div>
                    <div class="small-text">✓ 四个角落都有指示器</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 显示当前视口信息
        function updateViewportInfo() {
            const info = document.querySelector('.info');
            if (info) {
                const width = window.innerWidth;
                const height = window.innerHeight;
                console.log(`视口尺寸: ${width} x ${height}`);
                console.log(`缩放比例: ${window.devicePixelRatio || 1}`);
            }
        }
        
        window.addEventListener('resize', updateViewportInfo);
        window.addEventListener('load', updateViewportInfo);
    </script>
</body>
</html>
