<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/static/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
      /* 简单有效的缩放方案 */
      html {
        margin: 0;
        padding: 0;
        width: 100vw;
        height: 100vh;
        overflow: hidden;
      }

      body {
        margin: 0;
        padding: 0;
        width: 100vw;
        height: 100vh;
        overflow: hidden;
        background-color: #f9fafb;
        /* 使用transform缩放，保持在视口内 */
        transform: scale(0.9);
        transform-origin: 0 0;
      }

      #app {
        /* 补偿缩放，确保内容填满 */
        width: 111.11vw;
        height: 111.11vh;
        min-height: 111.11vh;
      }
    </style>
    <!-- 强制不使用缓存 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>信小财</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
