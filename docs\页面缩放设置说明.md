# 页面缩放设置说明

## 概述

本文档说明如何将页面缩放设置为90%，提供了多种实现方法。

## 实现方法

### 方法1：Viewport Meta标签设置（推荐）

**文件**: `index.html`

```html
<meta name="viewport" content="width=device-width, initial-scale=0.9, maximum-scale=0.9, minimum-scale=0.9, user-scalable=no">
```

**优点**:
- 简单直接
- 适用于移动端和桌面端
- 浏览器原生支持
- 不影响布局计算

**缺点**:
- 禁用了用户缩放功能
- 主要针对移动端设计

### 方法2：CSS Transform缩放（当前使用）

**文件**: `src/assets/main.css`

```css
#app {
  /* 设置整体缩放到90% */
  transform: scale(0.9);
  transform-origin: top left;
  /* 调整容器尺寸以适应缩放 */
  width: 111.11%; /* 100% / 0.9 = 111.11% */
}
```

**优点**:
- 精确控制缩放
- 保持用户缩放功能
- 适用于所有现代浏览器
- 可以动态调整

**缺点**:
- 需要调整容器尺寸
- 可能影响某些布局计算

### 方法3：CSS Zoom属性（备选）

**文件**: `src/assets/main.css`

```css
#app {
  zoom: 0.9;
}
```

**优点**:
- 最简单的实现
- 不需要调整容器尺寸
- 性能较好

**缺点**:
- 非标准属性
- Firefox支持有限
- 可能在某些浏览器中表现不一致

## 当前配置

项目当前使用了**Transform Scale方案**：

1. **HTML内联样式**: 在`index.html`中设置body的transform缩放
2. **尺寸补偿**: #app容器使用111.11vw×111.11vh补偿缩放后的空白

### 空白区域修复

采用了简化的解决方案，直接在HTML中处理缩放和空白填充：

#### 核心策略
1. **内联样式**: 在`index.html`中直接设置缩放样式
2. **尺寸计算**: 通过数学计算确定缩放后需要的容器尺寸
3. **简化层级**: 避免复杂的CSS层级，减少冲突

#### 具体实现
```html
<!-- index.html -->
<style>
  html {
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
  }

  body {
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background-color: #f9fafb;
    /* 使用transform缩放，保持在视口内 */
    transform: scale(0.9);
    transform-origin: 0 0;
  }

  #app {
    /* 补偿缩放，确保内容填满 */
    width: 111.11vw;
    height: 111.11vh;
    min-height: 111.11vh;
  }
</style>
```

#### 技术原理
- **缩放方式**: `transform: scale(0.9)` 在body级别
- **原点设置**: `transform-origin: 0 0` 从左上角开始缩放
- **尺寸补偿**: #app使用111.11vw×111.11vh填充缩放后的空白
- **溢出控制**: html和body都设置`overflow: hidden`

## 切换方法

### 如果只想使用Viewport方法：
1. 保持`index.html`中的viewport设置
2. 在`main.css`中注释掉transform相关属性：
   ```css
   /* transform: scale(0.9); */
   /* transform-origin: top left; */
   /* width: 111.11%; */
   ```

### 如果只想使用CSS Transform方法：
1. 将`index.html`中的viewport改回：
   ```html
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   ```
2. 保持`main.css`中的transform设置

### 如果想使用Zoom方法：
1. 将`index.html`中的viewport改回默认
2. 在`main.css`中替换transform为zoom：
   ```css
   #app {
     zoom: 0.9;
     /* 注释掉其他缩放相关属性 */
   }
   ```

## 测试建议

1. **桌面浏览器测试**: Chrome, Firefox, Safari, Edge
2. **移动设备测试**: iOS Safari, Android Chrome
3. **响应式测试**: 不同屏幕尺寸下的表现
4. **功能测试**: 确保表格、图表等组件正常显示

## 注意事项

1. **VTable组件**: 缩放可能影响表格的滚动和交互，需要测试
2. **ECharts图表**: 图表可能需要重新计算尺寸
3. **Element Plus组件**: 某些组件的定位可能受影响
4. **打印功能**: 缩放设置可能影响打印效果

## 调试技巧

1. 使用浏览器开发者工具检查元素尺寸
2. 测试不同缩放级别的效果
3. 检查是否有滚动条或布局问题
4. 验证交互功能是否正常

## 自定义缩放比例

如果需要其他缩放比例，只需修改相应的数值：

- **80%缩放**: `scale(0.8)`, `width: 125%` (100%/0.8)
- **85%缩放**: `scale(0.85)`, `width: 117.65%` (100%/0.85)
- **95%缩放**: `scale(0.95)`, `width: 105.26%` (100%/0.95)

公式：容器宽度 = 100% / 缩放比例
