<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transform Scale 缩放测试</title>
    <style>
      /* 与项目相同的缩放方案 */
      html {
        margin: 0;
        padding: 0;
        width: 100vw;
        height: 100vh;
        overflow: hidden;
      }
      
      body {
        margin: 0;
        padding: 0;
        width: 100vw;
        height: 100vh;
        overflow: hidden;
        background-color: #f9fafb;
        /* 使用transform缩放，保持在视口内 */
        transform: scale(0.9);
        transform-origin: 0 0;
      }
      
      #app {
        /* 补偿缩放，确保内容填满 */
        width: 111.11vw;
        height: 111.11vh;
        min-height: 111.11vh;
        background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
        background-size: 400% 400%;
        animation: gradientShift 10s ease infinite;
        position: relative;
      }
      
      .container {
        width: 100%;
        height: 100%;
        display: flex;
      }
      
      .sidebar {
        width: 240px;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-right: 1px solid rgba(255, 255, 255, 0.2);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        font-family: Arial, sans-serif;
      }
      
      .main-content {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        font-family: Arial, sans-serif;
        text-align: center;
        position: relative;
      }
      
      .test-box {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 40px;
        margin: 20px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      }
      
      .corner-indicator {
        position: absolute;
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.9);
        color: #333;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 12px;
        z-index: 1000;
      }
      
      .top-left {
        top: 0;
        left: 0;
        border-radius: 0 0 25px 0;
      }
      
      .top-right {
        top: 0;
        right: 0;
        border-radius: 0 0 0 25px;
      }
      
      .bottom-left {
        bottom: 0;
        left: 0;
        border-radius: 0 25px 0 0;
      }
      
      .bottom-right {
        bottom: 0;
        right: 0;
        border-radius: 25px 0 0 0;
      }
      
      @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
      }
      
      .info {
        font-size: 18px;
        margin: 10px 0;
      }
      
      .small-text {
        font-size: 14px;
        opacity: 0.8;
      }
      
      .debug-info {
        position: fixed;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-family: monospace;
        font-size: 12px;
        z-index: 2000;
      }
    </style>
</head>
<body>
    <div class="debug-info" id="debugInfo">
      缩放方案: transform scale(0.9)<br>
      容器尺寸: 111.11vw × 111.11vh
    </div>
    
    <div id="app">
        <div class="container">
            <!-- 角落指示器 -->
            <div class="corner-indicator top-left">左上</div>
            <div class="corner-indicator top-right">右上</div>
            <div class="corner-indicator bottom-left">左下</div>
            <div class="corner-indicator bottom-right">右下</div>
            
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="test-box">
                    <h3>侧边栏</h3>
                    <p class="small-text">240px 宽度</p>
                    <p class="small-text">100% 高度</p>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="main-content">
                <div class="test-box">
                    <h1>Transform Scale 测试</h1>
                    <div class="info">缩放方式: transform: scale(0.9)</div>
                    <div class="info">容器尺寸: 111.11vw × 111.11vh</div>
                    <div class="small-text">检查四个角落是否都有指示器</div>
                    <div class="small-text">确认没有空白区域</div>
                </div>
                
                <div class="test-box">
                    <h3>技术细节</h3>
                    <div class="small-text">✓ body使用transform: scale(0.9)</div>
                    <div class="small-text">✓ #app尺寸补偿到111.11%</div>
                    <div class="small-text">✓ transform-origin: 0 0</div>
                    <div class="small-text">✓ overflow: hidden防止滚动条</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function updateDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            const bodyRect = document.body.getBoundingClientRect();
            const appRect = document.getElementById('app').getBoundingClientRect();
            
            debugInfo.innerHTML = `
                缩放方案: transform scale(0.9)<br>
                视口: ${window.innerWidth} × ${window.innerHeight}<br>
                Body: ${bodyRect.width.toFixed(1)} × ${bodyRect.height.toFixed(1)}<br>
                App: ${appRect.width.toFixed(1)} × ${appRect.height.toFixed(1)}
            `;
        }
        
        window.addEventListener('load', updateDebugInfo);
        window.addEventListener('resize', updateDebugInfo);
        
        // 每秒更新一次调试信息
        setInterval(updateDebugInfo, 1000);
    </script>
</body>
</html>
