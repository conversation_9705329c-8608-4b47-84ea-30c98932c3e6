@import './base.css';

#app {
  margin: 0;
  padding: 0;
  font-weight: normal;
  /* 尺寸设置已移到index.html中 */
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

/* body的其他样式已在上面定义，这里只添加补充 */

@media (min-width: 1024px) {
  body {
    margin: 0;
    padding: 0;
  }

  #app {
    /* 响应式下保持缩放设置 */
    width: 111.11vw;
    height: 111.11vh;
    min-height: 111.11vh;
  }
}
